package com.connexin.pmx.server.services.impl

import com.connexin.atlas.sl.survey.dto.RemoteSurveyLinkDto
import com.connexin.atlas.sl.survey.dto.SurveyType
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.CreateMessageRequest
import com.connexin.pmx.server.models.dtos.CreateMessageResponse
import com.connexin.pmx.server.models.dtos.EngagementDataForMessageDto
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.services.*
import com.connexin.pmx.server.utils.use
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.stereotype.Component
import org.valiktor.ConstraintViolationException
import org.valiktor.i18n.mapToMessage
import java.time.Instant
import java.time.OffsetTime
import java.time.ZoneId
import java.util.*
import kotlin.collections.iterator

@Component
class EngagementMessageHandlerImpl(
    private val messageService: PmxMessageService,
    private val templateService: TemplateService,
    private val checkInService: CheckInService,
    private val surveyService: SurveyService,
) : EngagementMessageHandler {

    override fun send(
        engagement: Engagement,
        rule: EngagementRule,
        customer: Customer,
        contacts: Set<ContactResource>,
        appointments: Set<AppointmentResource>,
        scenario: TemplateScenario,
        rules: Map<EngagementWorkflow, EngagementRule>
    ): EngagementMessageResult {
        val languages = contacts.map { it.language }.toSet()
        val substitutions = templateService.buildSubstitutions(engagement, languages, customer)
        val location = engagement.resources.filterIsInstance<LocationResource>().first()
        var messageCount = 0
        val engagementData = EngagementDataForMessageDto(engagement, rule, substitutions, location, customer, scenario)
        var surveyLinksPerPatient = mapOf<String, List<RemoteSurveyLinkDto>>()
        if (rule.workflow == EngagementWorkflow.APPOINTMENT_SURVEY) {
            surveyLinksPerPatient = getSurveyLinksPerPatient(appointments.map { it.patient }.toSet(), customer)
        }

        for (contact in contacts) {
            when (rule.workflow) {
                EngagementWorkflow.CHECKIN -> {
                    for (appointment in appointments) {
                        arrayOf(
                            MDC.putCloseable(MdcKeys.MDC_APPOINTMENT_ID, appointment.id),
                            MDC.putCloseable(MdcKeys.MDC_PATIENT_ID, appointment.patient),
                            MDC.putCloseable(MdcKeys.MDC_CONTACT_ID, contact.id)
                        ).use {
                            val link = checkInService.generateCheckInLink(
                                appointment,
                                contact,
                                customer.id!!
                            )
                            engagementData.substitutions.keys.forEach {
                                engagementData.substitutions[it]?.set(SubstitutionTokens.CHECK_IN_LINK.token, link)
                            }

                            messageCount += sendInternal(engagementData, contact)
                        }
                    }
                }

                EngagementWorkflow.APPOINTMENT_SURVEY -> {
                    messageCount += handleAppointmentSurveys(engagementData, contact, surveyLinksPerPatient, rules)
                }

                else -> {
                    messageCount += sendInternal(engagementData, contact)
                }
            }
        }

        return if (engagementData.responses.all { !it.success }) {
            log.error("Unable to schedule a message for any specified contact, the engagement is unreachable!")
            EngagementMessageResult(success = false, contactErrors = engagementData.contactErrors)
        } else {
            log.info(
                "{} messages for {} contacts successfully created.",
                messageCount,
                contacts.size
            )
            EngagementMessageResult(success = true, contactErrors = engagementData.contactErrors)
        }
    }

    private fun handleAppointmentSurveys(
        engagementData: EngagementDataForMessageDto,
        contact: ContactResource, surveyLinksPerPatient: Map<String, List<RemoteSurveyLinkDto>>,
        rules: Map<EngagementWorkflow, EngagementRule>
    ): Int {
        var messageCount = 0
        for (patientSurveyLinkCollection in surveyLinksPerPatient) {
            MDC.putCloseable(MdcKeys.MDC_PATIENT_ID, patientSurveyLinkCollection.key).use {
                for (surveyLink in patientSurveyLinkCollection.value) {
                    arrayOf(
                        MDC.putCloseable(MdcKeys.MDC_SURVEY_QUEUE_ID, surveyLink.surveyQueueId.toString()),
                        MDC.putCloseable(MdcKeys.MDC_SURVEY_GUID, surveyLink.guid)
                    )
                    val surveyWorkflow = if (surveyLink.surveyType == SurveyType.INTERNAL)
                        EngagementWorkflow.APPT_SURVEY_INTERNAL else EngagementWorkflow.APPT_SURVEY_EXTERNAL

                    if (!rules.containsKey(surveyWorkflow)) {
                        continue
                    }

                    engagementData.substitutions.keys.forEach {
                        engagementData.substitutions[it]?.set(SubstitutionTokens.SURVEY_LINK.token, surveyLink.url)
                        if (surveyLink.surveyType == SurveyType.INTERNAL) {
                            engagementData.substitutions[it]?.set(SubstitutionTokens.PIN_CODE.token, surveyLink.pinCode)
                        }
                    }

                    messageCount += sendInternal(engagementData.copy(rule = rules[surveyWorkflow]!!), contact)
                }
            }
        }
        return messageCount
    }

    private fun getSurveyLinksPerPatient(
        patientIds: Set<String>, customer: Customer
    ): Map<String, List<RemoteSurveyLinkDto>> {

        val surveyLinksMap = mutableMapOf<String, List<RemoteSurveyLinkDto>>()
        for (patientId in patientIds) {
            val surveyLinks = surveyService.generateSurveyLinks(customer, patientId)
            if (!surveyLinks.isEmpty()) {
                surveyLinksMap[patientId] = trimExternalSurveyLinks(surveyLinks)
            }
        }

        return surveyLinksMap
    }

    /**
     * Since we want to limit our message number, we are trimming our external survey links since only the
     * first one is enough for entering to their landing page and can see the rest of pending surveys
     */
    private fun trimExternalSurveyLinks(surveyLinks: List<RemoteSurveyLinkDto>): List<RemoteSurveyLinkDto> {
        val trimmedSurveyLinks = mutableListOf<RemoteSurveyLinkDto>()
        var obtainedExternalLink = false

        for (surveyLink in surveyLinks) {
            if (surveyLink.surveyType == SurveyType.INTERNAL) {
                trimmedSurveyLinks.add(surveyLink)
            } else if (!obtainedExternalLink) {
                trimmedSurveyLinks.add(surveyLink)
                obtainedExternalLink = true
            }
        }

        return trimmedSurveyLinks
    }

    private fun sendInternal(
        engagementData: EngagementDataForMessageDto,
        contact: ContactResource
    ): Int {
        val response = try {
            sendToContact(
                engagementData.engagement,
                contact,
                engagementData.rule,
                engagementData.substitutions,
                engagementData.location,
                engagementData.customer,
                engagementData.scenario
            )
        } catch (ex: Exception) {
            log.error(
                "An unexpected exception occurred while trying to schedule an {} {} message for contact {}",
                contact.language,
                contact.contactMethod,
                contact.id,
                ex
            )
            CreateMessageResponse(
                success = false,
                errors = listOf(
                    ErrorDto(
                        path = "Contacts/${contact.id}",
                        message = "An unexpected error occurred trying to schedule a message for delivery",
                        Errors.UNEXPECTED_ERROR.code,
                        details = "CorrelationId=${MDC.get(MdcKeys.MDC_CORRELATION_ID)}"
                    )
                )
            )
        }

        engagementData.responses.add(response)

        if (response.success && response.message != null) {
            log.debug(
                "Scheduled message {} {} to contact {} for delivery",
                contact.contactMethod,
                response.message.id,
                contact.id
            )
            return MESSAGE_DELIVERY_SUCCESS
        } else if (!response.success) {
            log.error("Unable to schedule {} message to contact {}", contact.contactMethod, contact.id)
            engagementData.contactErrors.add(
                EngagementMessageResult.ContactErrors(
                    id = contact.id,
                    errors = response.errors ?: emptyList()
                )
            )
        }
        // Unsuccessful and skipped contact returns 0
        return MESSAGE_DELIVERY_FAILED
    }

    private fun sendToContact(
        engagement: Engagement,
        contact: ContactResource,
        rule: EngagementRule,
        substitutions: Map<Language, Map<String, String>>,
        location: LocationResource,
        customer: Customer,
        scenario: TemplateScenario
    ): CreateMessageResponse {
        val recipient = try {
            contact.coerceToRecipient()
        } catch (ex: ConstraintViolationException) {
            return CreateMessageResponse(
                success = false,
                errors = ex.constraintViolations
                    .mapToMessage(baseName = "messages", locale = Locale.ENGLISH)
                    .map {
                        val err = when (it.property) {
                            "email", "phone" -> Errors.INVALID_EMAIL_OR_PHONE
                            "contactMethod" -> Errors.INVALID_CONTACT_METHOD
                            else -> Errors.VALIDATION_FAILED
                        }
                        ErrorDto(
                            path = it.property,
                            details = it.message,
                            errorCode = err.code,
                            message = err.message
                        )
                    }
            )
        }
        val type = contact.contactMethod.toMessageType()

        // if the contact received a confirmation voice call, and they declined, the contact already knows
        // the appt is cancelled. Thus, we'll skip sending them this CANCELLATION template message to avoid annoying them.
        if (type == MessageType.VOICE && rule.workflow == EngagementWorkflow.CANCELLATION && engagement.contactDeclined) {
            log.debug("Skipping CANCELLATION voice call to contact {}, as the contact initiated the cancellation", contact.id)
            return CreateMessageResponse(success = true, message = null)
        }

        if (type != MessageType.SMS && rule.workflow == EngagementWorkflow.CONFIRMATION && scenario in setOf(
                TemplateScenario.CONFIRMATION_DEADLINE_PASSED,
                TemplateScenario.CONFIRMATION_CANNOT_CHANGE_RESPONSE
            )
        ) {
            log.debug("Skipping {} notification for {} contact", scenario, type)
            return CreateMessageResponse(success = true, message = null)
        }

        log.debug("Attempting to send {} message to contact with id: {}", type, contact.id)

        val segments = templateService.generateMessageSegments(rule, contact, substitutions[contact.language]!!, scenario)

        if (segments == null) {
            log.error(
                "No template was provided by customer for {}.{}.{}",
                rule.workflow,
                contact.contactMethod,
                contact.language
            )

            return CreateMessageResponse(
                success = false,
                errors = listOf(
                    ErrorDto(
                        path = "EngagementRules/${rule.id}",
                        details = "${rule.workflow}.${contact.contactMethod}.${contact.language} template is missing",
                        message = Errors.CONTACT_METHOD_OR_LANGUAGE_TEMPLATE_MISSING.message,
                        errorCode = Errors.CONTACT_METHOD_OR_LANGUAGE_TEMPLATE_MISSING.code
                    )
                )
            )
        }

        val (start, end) = buildSendWindow(location, contact.contactMethod, customer)

        val request = CreateMessageRequest(
            customerId = customer.id!!,
            customer = customer,
            type = type,
            to = setOf(recipient),
            message = segments.main,
            altMessage = segments.altMain,
            subject = segments.subject,
            instructions = segments.instructions,
            sendFrom = start,
            sendUntil = end,
            engagementId = engagement.id!!,
            engagementRuleId = rule.id,
            replyTo = when (type) {
                MessageType.EMAIL, MessageType.EMAIL_BROADCAST -> location.email
                else -> location.phone
            },
            language = contact.language
        )

        return messageService.create(request)
    }

    private fun buildSendWindow(
        location: LocationResource,
        method: ContactMethod,
        customer: Customer
    ): Pair<OffsetTime?, OffsetTime?> {
        if (method == ContactMethod.EMAIL) {
            log.debug("Using immediate sending window for EMAIL")
            return Pair(null, null)
        }
        val offset = ZoneId.of(location.zoneId).rules.getOffset(Instant.now())

        val window = Pair(
            OffsetTime.of(customer.deliveryStartTime, offset),
            OffsetTime.of(customer.deliveryEndTime, offset)
        )

        log.debug("Using {} sending window for {}", window, method)

        return window
    }

    companion object {
        private val log = LoggerFactory.getLogger(EngagementMessageHandlerImpl::class.java)
        private const val MESSAGE_DELIVERY_FAILED = 0
        private const val MESSAGE_DELIVERY_SUCCESS = 1
    }
}