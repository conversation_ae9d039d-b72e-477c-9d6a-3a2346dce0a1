package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.services.*
import com.connexin.urlformattingtool.model.ShortenOptions
import com.connexin.urlformattingtool.service.UrlShortener
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.temporal.ChronoUnit

@Service
class CheckInServiceImpl(
    private val bridgeService: BridgeService,
    private val urlShortener: UrlShortener
) : CheckInService {

    override fun generateCheckInLink(appointment: AppointmentResource, contact: ContactResource, opmedId: String): String? {
        return try {
            log.info("Generating check-in link for appointment, patient, and contact")
            val linkResult = bridgeService.generatePatientCheckinUrl(appointment.id, appointment.patient, contact.id, opmedId)

            log.info("Shortening check-in link for appointment, patient, and contact")
            urlShortener.shortenUrl(linkResult, ShortenOptions(appointment.startTime.plus(URL_EXPIRATION_OFFSET_HOURS, ChronoUnit.HOURS)))
        } catch (exception: Exception) {
            log.error("Failed to generate check-in link for appointment ${appointment.id}, patient ${appointment.patient}, contact ${contact.id}, opmedId $opmedId", exception)
            null
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(CheckInServiceImpl::class.java)
        private const val URL_EXPIRATION_OFFSET_HOURS = 4L
    }
}