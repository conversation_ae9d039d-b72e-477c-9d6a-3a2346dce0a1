logging:
  level:
    com.connexin.pmx.server: ${ENV_LOGGING_LEVEL_PMX:DEBUG}
op:
  atlas:
    practice:
      url: ${ENV_ATLAS_PRACTICE_URL:https://atlas-dev.op.healthcare/api/v1/atlas-practice/}
    survey:
      url: ${ENV_ATLAS_SURVEY_URL:https://atlas-dev.op.healthcare/api/v1/atlas-survey/}
    token-url: ${ENV_ATLAS_TOKEN_URL:https://officepracticum-test.auth0.com/oauth/token}
    client-id: ${ENV_ATLAS_CLIENT_ID:jR2HFiGRhJsWU7hwkHaLBheMnukcJu63}
    client-secret: ${ENV_ATLAS_CLIENT_SECRET:vbdsbZyRfhw2v3KelfgUlSlqBx64lI-jhjVag74IGVZ0yc0gxcepMfzhIE6a4WkQ}
    audience: ${ENV_ATLAS_AUDIENCE:https://api.officepracticum.com/atlas}
  pmx:
    base-url: ${ENV_PMX_BASE_URL:http://localhost:8080}
    confirmation-base-url: ${ENV_PMX_CONFIRMATION_BASE_URL:http://localhost:8080}
    environment: LOCAL
    admin-password: test
    cache:
      customers-duration: 1 # 1 minute
    jobs:
      delivery-stats:
        cron: ${ENV_PMX_JOBS_DELIVERY_STATS_CRON:-}
spring:
  data:
    mongodb:
      uri: *****************************************************************
  thymeleaf:
    cache: false
redis:
  nodes: redis://localhost:6379
